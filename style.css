* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab, #667eea, #764ba2);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 20px;
}

/* Fingerprint Section */
.fingerprint-section {
    text-align: center;
    opacity: 0;
    transition: all 0.8s ease;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.fingerprint-section.active {
    opacity: 1;
}

.fingerprint-scanner {
    position: relative;
    width: 200px;
    height: 200px;
    margin: 0 auto 40px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(15px);
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.fingerprint-scanner:hover {
    transform: scale(1.05);
    box-shadow: 0 15px 40px rgba(255, 255, 255, 0.3);
}

.fingerprint-icon {
    font-size: 80px;
    animation: pulse 2s infinite;
}

.scan-line {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, transparent, #ff6b9d, transparent);
    opacity: 0;
    animation: scan 2s infinite;
}

.ripple {
    position: absolute;
    top: -15px;
    left: -15px;
    right: -15px;
    bottom: -15px;
    border: 3px solid rgba(255, 107, 157, 0.5);
    border-radius: 50%;
    opacity: 0;
    animation: ripple 2s infinite;
}

.instruction {
    color: white;
    font-size: 20px;
    margin-bottom: 30px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.progress-bar {
    width: 250px;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    margin: 0 auto 30px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff6b9d, #ffd93d);
    width: 0%;
    transition: width 5s ease;
    border-radius: 4px;
}

/* Name reveal during scanning */
.scanning-name {
    color: white;
    font-size: 32px;
    font-weight: bold;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    margin-top: 20px;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.5s ease;
}

.scanning-name .typing-text {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s ease;
}

.scanning-name .typing-text.show {
    opacity: 1;
    transform: scale(1);
}

.scanning-name .cursor {
    display: inline-block;
    width: 3px;
    height: 32px;
    background: white;
    margin-left: 2px;
    animation: blink 1s infinite;
    opacity: 0;
}

.scanning-name .cursor.show {
    opacity: 1;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.scanning-name.reveal {
    opacity: 1;
    transform: scale(1);
}

/* Name Section - Hidden by default */
.name-section {
    display: none;
}

/* Notes Container */
.notes-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 600px;
    height: 450px;
    display: none;
    opacity: 0;
    transition: all 0.8s ease;
}

.notes-container.show {
    opacity: 1;
}

.note-card {
    position: absolute;
    width: 600px;
    height: 450px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 25px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    user-select: none;
    padding: 50px;
}

.note-card[data-index="0"] {
    z-index: 3;
    transform: rotate(-1deg);
}

.note-card[data-index="1"] {
    z-index: 2;
    transform: rotate(0.5deg) translateY(8px);
}

.note-card[data-index="2"] {
    z-index: 1;
    transform: rotate(-0.5deg) translateY(16px);
}

.note-card.dragging {
    z-index: 10;
    transition: none;
}

.note-lock {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 100px;
    z-index: 10;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.note-content {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.5s ease;
    text-align: center;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 1;
}

.note-card.unlocked .note-lock {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0);
    pointer-events: none;
}

.note-card.unlocked .note-content {
    opacity: 1;
    transform: translateY(0);
}

.note-header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;
    font-weight: bold;
    color: #333;
    font-size: 28px;
}

.note-icon {
    margin-right: 15px;
    font-size: 36px;
}

.note-text {
    color: #555;
    font-size: 24px;
    line-height: 1.6;
    margin-bottom: 30px;
    text-align: center;
    max-width: 500px;
}

.note-sticker {
    font-size: 50px;
    animation: bounce 2s infinite;
}

.drag-hint {
    position: absolute;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    color: #888;
    font-size: 18px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.note-card.unlocked .drag-hint {
    opacity: 1;
}

/* Final Message */
.final-message {
    text-align: center;
    opacity: 0;
    transform: scale(0.8);
    transition: all 1s ease;
    display: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.final-message.active {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

.final-text {
    font-size: 80px;
    color: white;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    margin-bottom: 30px;
    animation: simpleGlow 2s ease-in-out infinite alternate;
    font-weight: bold;
    letter-spacing: 3px;
}

.final-hearts {
    font-size: 60px;
    animation: simpleFloat 3s ease-in-out infinite;
}

/* Footer */
.footer {
    position: fixed;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: white;
    opacity: 0;
    transition: opacity 1s ease;
    display: none;
}

.footer.active {
    opacity: 1;
}

.cute-stickers {
    margin-top: 10px;
}

.cute-stickers span {
    display: inline-block;
    margin: 0 10px;
    font-size: 25px;
    animation: wiggle 2s infinite;
    animation-delay: calc(var(--i) * 0.2s);
}

/* Animations */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes simpleGlow {
    0% { text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); }
    100% { text-shadow: 0 4px 20px rgba(255, 255, 255, 0.5); }
}

@keyframes simpleFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes scan {
    0% { top: 0; opacity: 0; }
    50% { opacity: 1; }
    100% { top: 100%; opacity: 0; }
}

@keyframes ripple {
    0% { transform: scale(1); opacity: 0.5; }
    100% { transform: scale(1.5); opacity: 0; }
}

@keyframes bounce {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes wiggle {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(3deg); }
    75% { transform: rotate(-3deg); }
}

/* Responsive */
@media (max-width: 768px) {
    .notes-container {
        width: 350px;
        height: 300px;
    }
    
    .note-card {
        width: 350px;
        height: 300px;
        padding: 25px;
    }
    
    .fingerprint-scanner {
        width: 160px;
        height: 160px;
    }
    
    .fingerprint-icon {
        font-size: 70px;
    }
    
    .final-text {
        font-size: 48px;
    }
    
    .note-header {
        font-size: 20px;
    }
    
    .note-text {
        font-size: 16px;
    }
    
    .note-lock {
        font-size: 60px;
    }
    
    .scanning-name {
        font-size: 24px;
    }
}

.panda-trigger {
    margin-top: 30px;
    padding: 15px 30px;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 25px;
    color: white;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    user-select: none;
}

.panda-trigger:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
    box-shadow: 0 10px 30px rgba(255, 255, 255, 0.2);
}

.panda-container {
    margin-top: 20px;
    opacity: 0;
    transform: scale(0);
    transition: all 0.5s ease;
}

.panda-container.show {
    opacity: 1;
    transform: scale(1);
}

.panda-gif {
    width: 200px;
    height: 200px;
    border-radius: 20px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
    object-fit: cover;
}

@media (max-width: 768px) {
    .panda-trigger {
        font-size: 16px;
        padding: 12px 25px;
    }
    
    .panda-gif {
        width: 150px;
        height: 150px;
    }
}


