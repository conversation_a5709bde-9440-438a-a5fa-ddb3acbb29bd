* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(-45deg, #ff9a9e, #fecfef, #fecfef, #ffecd2);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
}

#particles-js {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 1;
}

.container {
    position: relative;
    z-index: 10;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 20px;
}

/* Balloons */
.balloons {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 5;
    pointer-events: none;
}

.balloon {
    position: absolute;
    font-size: 40px;
    animation: float 6s ease-in-out infinite;
}

.balloon-1 {
    left: 10%;
    top: 20%;
    animation-delay: 0s;
}

.balloon-2 {
    left: 80%;
    top: 15%;
    animation-delay: 1s;
}

.balloon-3 {
    left: 15%;
    top: 60%;
    animation-delay: 2s;
}

.balloon-4 {
    left: 85%;
    top: 70%;
    animation-delay: 3s;
}

.balloon-5 {
    left: 20%;
    top: 80%;
    animation-delay: 4s;
}

/* Cake */
.cake-container {
    margin-bottom: 40px;
    animation: bounceIn 2s ease;
}

.cake {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.cake-layer {
    border-radius: 20px;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.cake-bottom {
    width: 200px;
    height: 60px;
    background: linear-gradient(45deg, #8B4513, #D2691E);
    margin-bottom: -10px;
}

.cake-middle {
    width: 160px;
    height: 50px;
    background: linear-gradient(45deg, #FFB6C1, #FF69B4);
    margin-bottom: -10px;
}

.cake-top {
    width: 120px;
    height: 40px;
    background: linear-gradient(45deg, #98FB98, #90EE90);
}

.candles {
    position: absolute;
    top: -30px;
    display: flex;
    gap: 20px;
}

.candle {
    width: 8px;
    height: 25px;
    background: #FFD700;
    border-radius: 4px;
    position: relative;
}

.wick {
    position: absolute;
    top: -3px;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 5px;
    background: #333;
}

.flame {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 8px;
    height: 12px;
    background: radial-gradient(circle, #FFD700, #FF4500);
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    animation: flicker 0.5s ease-in-out infinite alternate;
}

/* Birthday message */
.birthday-message {
    text-align: center;
    margin-bottom: 40px;
}

.main-title {
    font-size: 48px;
    color: #FF1493;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.3);
    margin-bottom: 20px;
    animation: rainbow 3s linear infinite;
}

.name-title {
    font-size: 36px;
    color: #4169E1;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    margin-bottom: 10px;
    animation: glow 2s ease-in-out infinite alternate;
}

.date {
    font-size: 28px;
    color: #FF6347;
    font-weight: bold;
    margin-bottom: 30px;
    animation: pulse 2s infinite;
}

.wishes {
    max-width: 600px;
    margin: 0 auto;
}

.wish-text {
    font-size: 20px;
    color: #2E8B57;
    margin-bottom: 15px;
    opacity: 0;
    animation: fadeInUp 1s ease forwards;
}

.wish-text:nth-child(1) { animation-delay: 1s; }
.wish-text:nth-child(2) { animation-delay: 2s; }
.wish-text:nth-child(3) { animation-delay: 3s; }

/* Interactive buttons */
.interactive-section {
    display: flex;
    gap: 20px;
    margin-bottom: 40px;
    flex-wrap: wrap;
    justify-content: center;
}

.interactive-section button {
    padding: 15px 25px;
    font-size: 18px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    font-weight: bold;
}

.blow-candles-btn {
    background: linear-gradient(45deg, #87CEEB, #4682B4);
    color: white;
}

.confetti-btn {
    background: linear-gradient(45deg, #FFB6C1, #FF69B4);
    color: white;
}

.music-btn {
    background: linear-gradient(45deg, #98FB98, #32CD32);
    color: white;
}

.interactive-section button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Gift box */
.gift-container {
    text-align: center;
}

.gift-box {
    position: relative;
    width: 100px;
    height: 100px;
    margin: 0 auto 20px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.gift-box:hover {
    transform: scale(1.1) rotate(5deg);
}

.gift-body {
    width: 100px;
    height: 70px;
    background: linear-gradient(45deg, #FF6347, #FF4500);
    border-radius: 10px;
    position: absolute;
    bottom: 0;
}

.gift-lid {
    width: 110px;
    height: 30px;
    background: linear-gradient(45deg, #FFD700, #FFA500);
    border-radius: 10px;
    position: absolute;
    top: 0;
    left: -5px;
    transition: transform 0.5s ease;
}

.gift-ribbon-v {
    width: 15px;
    height: 100px;
    background: linear-gradient(45deg, #32CD32, #228B22);
    position: absolute;
    left: 42.5px;
    top: 0;
}

.gift-ribbon-h {
    width: 100px;
    height: 15px;
    background: linear-gradient(45deg, #32CD32, #228B22);
    position: absolute;
    left: 0;
    top: 42.5px;
}

.gift-bow {
    width: 30px;
    height: 20px;
    background: linear-gradient(45deg, #FF1493, #DC143C);
    border-radius: 50%;
    position: absolute;
    left: 35px;
    top: -10px;
}

.gift-text {
    font-size: 18px;
    color: #4169E1;
    font-weight: bold;
}

/* Gift modal */
.gift-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.gift-modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 40px;
    border-radius: 20px;
    width: 80%;
    max-width: 500px;
    text-align: center;
    position: relative;
    animation: modalSlideIn 0.5s ease;
}

.close-modal {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    right: 20px;
    top: 15px;
}

.close-modal:hover {
    color: #000;
}

.surprise-content h2 {
    color: #FF1493;
    margin-bottom: 20px;
}

.special-message {
    margin-top: 20px;
    padding: 20px;
    background: linear-gradient(45deg, #FFE4E1, #FFF0F5);
    border-radius: 15px;
}

/* Animations */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
}

@keyframes flicker {
    0% { transform: translateX(-50%) scale(1) rotate(-1deg); }
    100% { transform: translateX(-50%) scale(1.1) rotate(1deg); }
}

@keyframes rainbow {
    0% { color: #FF1493; }
    16% { color: #FF6347; }
    33% { color: #FFD700; }
    50% { color: #32CD32; }
    66% { color: #4169E1; }
    83% { color: #9370DB; }
    100% { color: #FF1493; }
}

@keyframes glow {
    0% { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3); }
    100% { text-shadow: 2px 2px 20px rgba(65, 105, 225, 0.8); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes modalSlideIn {
    0% {
        opacity: 0;
        transform: translateY(-50px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .main-title {
        font-size: 32px;
    }
    
    .name-title {
        font-size: 24px;
    }
    
    .date {
        font-size: 20px;
    }
    
    .wish-text {
        font-size: 16px;
    }
    
    .interactive-section {
        flex-direction: column;
        align-items: center;
    }
    
    .interactive-section button {
        width: 200px;
    }
    
    .balloon {
        font-size: 30px;
    }
    
    .cake-bottom {
        width: 150px;
        height: 45px;
    }
    
    .cake-middle {
        width: 120px;
        height: 38px;
    }
    
    .cake-top {
        width: 90px;
        height: 30px;
    }
}
